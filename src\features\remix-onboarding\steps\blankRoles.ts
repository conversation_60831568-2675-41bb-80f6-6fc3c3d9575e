// Utility to get a blank InitialRoles object (all false except notInMusic if specified)
import { InitialRoles } from './type';

export function getBlankRoles(notInMusic: boolean = false): InitialRoles {
  return {
    toplineWriter: false,
    lyricist: false,
    melodyWriter: false,
    beatmakerTrackmaker: false,
    arranger: false,
    remixer: false,
    orchestrator: false,
    filmTvComposer: false,
    jingleWriter: false,
    vocalProducer: false,
    mixingProducer: false,
    recordingProducer: false,
    arrangementProducer: false,
    synthProducer: false,
    soundDesigner: false,
    orchestralProducer: false,
    executiveProducer: false,
    beatmaker: false,
    remixerProducer: false,
    guitarist: false,
    drummer: false,
    keyboardist: false,
    stringsPlayer: false,
    woodwindPlayer: false,
    brassPlayer: false,
    folkInstrumentalist: false,
    electronicInstrumentalist: false,
    worldInstrumentalist: false,
    sessionMusician: false,
    multiInstrumentalist: false,
    orchestraMember: false,
    leadVocalist: false,
    backgroundVocalist: false,
    sessionVocalist: false,
    choirSinger: false,
    operaSinger: false,
    vocalArranger: false,
    beatboxer: false,
    rapVocalist: false,
    voiceoverArtist: false,
    vocalCoach: false,
    recordingEngineer: false,
    mixingEngineer: false,
    masteringEngineer: false,
    audioEditor: false,
    soundDesignerEngineer: false,
    vocalEditor: false,
    liveSoundEngineer: false,
    studioTechnician: false,
    audioRestoration: false,
    broadcastEngineer: false,
    soloArtist: false,
    bandMember: false,
    djArtist: false,
    livePerformer: false,
    coverArtist: false,
    tributeArtist: false,
    musicalTheater: false,
    classicalPerformer: false,
    busker: false,
    performanceArtist: false,
    artistManager: false,
    bookingAgent: false,
    musicPromoter: false,
    aAndR: false,
    musicPublisher: false,
    musicAttorney: false,
    tourManager: false,
    musicMarketer: false,
    graphicDesigner: false,
    videoDirector: false,
    musicJournalist: false,
    radioDJ: false,
    musicSupervisor: false,
    musicEducator: false,
    other: false,
    notInMusic: notInMusic,
  };
}
