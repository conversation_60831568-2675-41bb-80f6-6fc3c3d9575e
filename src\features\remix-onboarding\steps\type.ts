export type InitialRoles = {
  // Songwriter/Composer
  toplineWriter: boolean;
  lyricist: boolean;
  melodyWriter: boolean;
  beatmakerTrackmaker: boolean;
  arranger: boolean;
  remixer: boolean;
  orchestrator: boolean;
  filmTvComposer: boolean;
  jingleWriter: boolean;

  // Producer
  vocalProducer: boolean;
  mixingProducer: boolean;
  recordingProducer: boolean;
  arrangementProducer: boolean;
  synthProducer: boolean;
  soundDesigner: boolean;
  orchestralProducer: boolean;
  executiveProducer: boolean;
  beatmaker: boolean;
  remixerProducer: boolean;

  // Musician
  guitarist: boolean;
  drummer: boolean;
  keyboardist: boolean;
  stringsPlayer: boolean;
  woodwindPlayer: boolean;
  brassPlayer: boolean;
  folkInstrumentalist: boolean;
  electronicInstrumentalist: boolean;
  worldInstrumentalist: boolean;
  sessionMusician: boolean;
  multiInstrumentalist: boolean;
  orchestraMember: boolean;
  bassPlayer: boolean;

  // Vocalist
  leadVocalist: boolean;
  backgroundVocalist: boolean;
  sessionVocalist: boolean;
  choirSinger: boolean;
  operaSinger: boolean;
  vocalArranger: boolean;
  beatboxer: boolean;
  rapVocalist: boolean;
  voiceoverArtist: boolean;
  vocalCoach: boolean;

  // Engineer/Editor
  recordingEngineer: boolean;
  mixingEngineer: boolean;
  masteringEngineer: boolean;
  audioEditor: boolean;
  soundDesignerEngineer: boolean;
  vocalEditor: boolean;
  liveSoundEngineer: boolean;
  studioTechnician: boolean;
  audioRestoration: boolean;
  broadcastEngineer: boolean;

  // Artist/Performer
  soloArtist: boolean;
  bandMember: boolean;
  djArtist: boolean;
  livePerformer: boolean;
  coverArtist: boolean;
  tributeArtist: boolean;
  musicalTheater: boolean;
  classicalPerformer: boolean;
  busker: boolean;
  performanceArtist: boolean;
  


  // Other Music Industry
  artistManager: boolean;
  bookingAgent: boolean;
  musicPromoter: boolean;
  aAndR: boolean;
  musicPublisher: boolean;
  musicAttorney: boolean;
  tourManager: boolean;
  musicMarketer: boolean;
  graphicDesigner: boolean;
  videoDirector: boolean;
  musicJournalist: boolean;
  radioDJ: boolean;
  musicSupervisor: boolean;
  musicEducator: boolean;
  other: boolean;
  creativeDirector: boolean;
visualContentCreator: boolean;
tourProduction: boolean;
recordLabel: boolean;
publicist: boolean;

  // Not in Music
  notInMusic: boolean;

  // Custom value for Other
  otherCustom?: string;
};

export  type RoleKey = keyof InitialRoles;

export interface RoleGroup {
  id: string;
  title: string;
  roles: Array<{
    key: RoleKey;
    label: string;
  }>;
}
